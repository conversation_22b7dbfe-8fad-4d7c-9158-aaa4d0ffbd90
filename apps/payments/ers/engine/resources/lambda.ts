import { ServerlessFunction } from '@npco/component-bff-serverless';
import {
  getEntityTableName,
  getOrdersTableName,
  getEntityTableArn,
  getOrdersTableArn,
  getCatalogsTableName,
} from './common';

const stage = process.env.STAGE || '';
export const createSendReceiptRequestLambdaHandler = (): ServerlessFunction => ({
  handler: `src/index.sendReceiptHandler`,
  name: `send<PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>`,
  timeout: '${env:LAMBDA_TIMEOUT_IN_SECONDS}',
  policy: {
    useVpc: true,
    useXray: true,
    inline: {
      invokeCommandHandlerPolicy: [
        {
          actions: ['lambda:invokeFunction'],
          resources: [
            'arn:aws:lambda:${self:provider.region}:${self:custom.accountId}:function:${self:custom.ersCqrsCommandHandler}*',
          ],
        },
      ],
      ec2Policy: [
        {
          actions: ['ec2:CreateNetworkInterface', 'ec2:DescribeNetworkInterfaces', 'ec2:DeleteNetworkInterface'],
          resources: ['*'],
        },
      ],
      snsPolicy: [
        {
          actions: ['sns:Publish'],
          resources: ['*'],
        },
      ],
      s3Policy: [
        {
          actions: ['s3:PutObject'],
          resources: ['arn:aws:s3:::${self:custom.receiptBucket}/*'],
        },
      ],
      sesPolicy: [
        {
          actions: ['ses:SendEmail', 'ses:SendRawEmail'],
          resources: ['*'],
        },
      ],
      logPolicy: [
        {
          actions: ['logs:CreateLogStream', 'logs:PutLogEvents', 'logs:CreateLogGroup'],
          resources: [
            'arn:aws:logs:${self:provider.region}:${self:custom.accountId}:log-group:/aws/lambda/${self:provider.stackName}-sendReceiptHandler:*',
          ],
        },
      ],
      ssmPolicy: [
        {
          actions: ['ssm:GetParameters'],
          resources: [
            'arn:aws:ssm:${self:provider.region}:${self:custom.accountId}:parameter/${env:VPC_ENV_NAME}-messagemedia-api-key',
            'arn:aws:ssm:${self:provider.region}:${self:custom.accountId}:parameter/${env:VPC_ENV_NAME}-messagemedia-api-secret',
          ],
        },
      ],
      entityTableQueryPolicy: [
        {
          actions: ['dynamodb:GetItem', 'dynamodb:Query'],
          resources: [getEntityTableArn()],
        },
      ],
      ordersTableQueryPolicy: [
        {
          actions: ['dynamodb:GetItem', 'dynamodb:Query'],
          resources: [getOrdersTableArn()],
        },
      ],
    },
  },
  events: [
    {
      eventBridge: {
        eventBus: '${self:custom.eventBusArn}',
        pattern: {
          source: ['ers'],
          'detail-type': [
            'ers.Transaction.SendReceiptRequest',
            'ers.Transaction.SendReceiptRequestNoCardholder',
            'ers.Order.SendReceiptRequest',
          ],
        },
      },
    },
  ],
  environment: {
    COMPONENT_TABLE: getEntityTableName(),
    ORDERS_TABLE: getOrdersTableName(),
    CATALOGS_TABLE: getCatalogsTableName(),
    AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
    AMS_API_ENDPOINT_VERSION: '${env:AMS_API_ENDPOINT_VERSION}',
    CMD_ERS_CQRS: '${self:custom.ersCqrsCommandHandler}',
    S3_RECEIPTS_BUCKET: '${self:custom.receiptBucket}',
    RECEIPT_HTML_ASSETS_URL: '${env:RECEIPT_HTML_ASSETS_URL}receipt/assets/',
    SMS_RECEIPT_URL: '${env:RECEIPT_URL_NAME}',
    POS_RECEIPT_URL_NAME: '${env:POS_RECEIPT_URL_NAME}',
    RECEIPT_SOURCE_EMAIL: '${env:RECEIPT_SOURCE_EMAIL}',
    OPT_OUT_RECEIPTS_BASE_URL: '${env:OPT_OUT_RECEIPTS_BASE_URL}',
    STAGE: stage,
    MESSAGE_MEDIA_ENABLED: '${env:MESSAGE_MEDIA_ENABLED}',
    LOG_LEVEL: '${env:LOG_LEVEL}',
    DASHBOARD_BASE_URL: '${env:DASHBOARD_BASE_URL}',
  },
});

export const createGetReceiptPdfUrlLambdaHandler = (): ServerlessFunction => ({
  handler: `src/index.getReceiptPdfUrlHandler`,
  name: `getReceiptPdfUrl`,
  timeout: '${env:LAMBDA_TIMEOUT_IN_SECONDS}',
  policy: {
    useVpc: true,
    useXray: true,
    inline: {
      ec2Policy: [
        {
          actions: ['ec2:CreateNetworkInterface', 'ec2:DescribeNetworkInterfaces', 'ec2:DeleteNetworkInterface'],
          resources: ['*'],
        },
      ],
      s3Policy: [
        {
          actions: ['s3:PutObject', 's3:GetObject'],
          resources: ['arn:aws:s3:::${self:custom.receiptBucket}/*'],
        },
      ],
      logPolicy: [
        {
          actions: ['logs:CreateLogStream', 'logs:PutLogEvents', 'logs:CreateLogGroup'],
          resources: [
            'arn:aws:logs:${self:provider.region}:${self:custom.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getReceiptPdfUrl:*',
          ],
        },
      ],
      entityTableQueryPolicy: [
        {
          actions: ['dynamodb:GetItem', 'dynamodb:Query'],
          resources: [getEntityTableArn()],
        },
      ],
      ordersTableQueryPolicy: [
        {
          actions: ['dynamodb:GetItem', 'dynamodb:Query'],
          resources: [getOrdersTableArn()],
        },
      ],
    },
  },
  events: [
    {
      http: {
        path: 'v1/receipt/{id}/pdf-presigned-url',
        method: 'get',
        integration: 'lambda',
        request: {
          template: {
            'application/json': '{"id": "$input.params("id")", "reference": "$input.params("rrn")" }',
          },
          parameters: {
            paths: {
              id: true,
            },
            querystrings: {
              rrn: true,
            },
          },
        },
      },
    },
  ],
  environment: {
    COMPONENT_TABLE: getEntityTableName(),
    ORDERS_TABLE: getOrdersTableName(),
    CATALOGS_TABLE: getCatalogsTableName(),
    AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
    AMS_API_ENDPOINT_VERSION: '${env:AMS_API_ENDPOINT_VERSION}',
    S3_RECEIPTS_BUCKET: '${self:custom.receiptBucket}',
    RECEIPT_HTML_ASSETS_URL: '${env:RECEIPT_HTML_ASSETS_URL}receipt/assets/',
    OPT_OUT_RECEIPTS_BASE_URL: '${env:OPT_OUT_RECEIPTS_BASE_URL}',
    SMS_RECEIPT_URL: '${env:RECEIPT_URL_NAME}',
    POS_RECEIPT_URL_NAME: '${env:POS_RECEIPT_URL_NAME}',
    PDF_RECEIPT_URL: '${env:PDF_RECEIPT_URL_NAME}',
    RECEIPT_SOURCE_EMAIL: '${env:RECEIPT_SOURCE_EMAIL}',
    LOG_LEVEL: '${env:LOG_LEVEL}',
  },
});
