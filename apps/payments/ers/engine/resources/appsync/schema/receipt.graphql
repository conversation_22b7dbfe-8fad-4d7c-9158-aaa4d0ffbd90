type Query {
  getReceipt(transactionUuid: ID!): Receipt!
  getReceiptDownloadUrl(transactionUuid: ID!, format: ReceiptFileFormat!): ReceiptDownload!
  getPosReceipt(orderUuid: ID!): PosReceipt!
  getPosReceiptDownloadUrl(orderUuid: ID!, format: ReceiptFileFormat!): ReceiptDownload!
}

type Receipt {
  # transaction data
  transactionUuid: ID!
  timestamp: AWSDateTime!
  timestampLocal: AWSDateTime!
  type: ReceiptType!
  reference: String
  rrn: String
  totalDiscount: String
  totalServiceCharge: String
  totalAmount: String!
  totalGst: String!
  totalSurcharge: String
  totalTips: String
  subtotalAmount: String!
  saleAmount: String!
  status: TransactionStatus!
  scheme: CardScheme!
  maskedPan: String!
  emvAid: String
  emvTagsPrint: String
  emvAppName: String
  taxInvoice: Boolean!
  cardMedia: CardMedia
  account: CardApplicationAccount
  # receipt settings
  businessName: String
  siteName: String
  catid: String
  address: SiteAddress
  abn: String
  acn: String
  email: String
  website: String
  phone: String
  logo: String
  returnMessage: String
  message: String
  twitter: String
  facebook: String
  instagram: String
  linkedin: String
  orderReferenceNumber: String
  orderReceiptUrl: String
  items: [TransactionItem!]
  orderDiscounts: [TransactionDiscount!]
  orderServiceCharges: [TransactionServiceCharge!]
  catalogSettings: CatalogSettingsSnapshot
  currency: String
}

type CatalogSettingsSnapshot {
  autoSkuEnabled: Boolean
  itemsApplyTax: Boolean
  itemsTaxInclusive: Boolean
}

enum CardApplicationAccount {
  SAVINGS
  CHEQUE
  CREDIT
}

enum CardScheme {
  VISA
  MC
  AMEX
  JCB
  EFTPOS
  CUP
  DINERS
  OTHER
}

enum ReceiptType {
  REFUND
  PAYMENT
  INVOICE
  SALE
}

enum TransactionStatus {
  APPROVED
  DECLINED
}

type SiteAddress {
  street: String
  suburb: String
  state: String
  postcode: String
  country: String
}

enum CardMedia {
  MANUAL
  MSR
  ICC
  PICC
  NFC
  CNP
}

type ReceiptDownload {
  downloadLink: String!
  expire: AWSDateTime!
}

enum ReceiptFileFormat {
  PDF
  PNG
}

type TransactionItem {
  id: ID!
  catalogItemUuid: ID
  type: OrderItemType!
  attributes: [OrderItemAttribute!]
  name: String!
  price: String!
  quantity: Float!
  unit: CatalogUnit!
  ordinal: Int!
  description: String
  discounts: [TransactionDiscount!]
  serviceCharges: [TransactionServiceCharge!]
  taxes: [CatalogTax!]
  modifiers: [TransactionItemModifier!]
  variantName: String
  subtotalAmount: String
  totalAmount: String
  reportingCategoryColor: String
  images: [TransactionItemImage!]
}

type TransactionItemImage {
  id: ID!
  sizes: [TransactionItemImageSize!]
}

type TransactionItemImageSize {
  url: String!
  size: CatalogImageSize!
}

type TransactionDiscount {
  id: ID!
  catalogDiscountUuid: ID
  name: String!
  config: CatalogDiscountConfig!
  value: String!
  discountedAmount: String!
  ordinal: Int!
}

type TransactionServiceCharge {
  id: ID!
  catalogServiceChargeUuid: ID
  name: String!
  config: CatalogServiceChargeConfig!
  value: String!
  serviceChargeAmount: String!
  ordinal: Int!
}

type TransactionItemModifier {
  id: ID!
  catalogModifierUuid: ID # undefined if one-time modifier
  name: String!
  price: String!
  ordinal: Int!
  description: String
  unit: CatalogUnit!
  quantity: Float!
  subtotalAmount: String
}

type OrderItemAttribute {
  attributeName: String!
  attributeValue: String!
}

enum OrderItemType {
  SINGLE
  VARIANT
  ONE_TIME
}

enum CatalogUnit {
  QUANTITY
  HOUR
  DAY
}

type CatalogTax {
  enabled: Boolean!
  name: String!
  percent: Int
}

enum CatalogDiscountConfig {
  PERCENTAGE
  AMOUNT
}

enum CatalogServiceChargeConfig {
  PERCENTAGE
  AMOUNT
}

enum OrderPaymentType {
  CASH
  CARD
  OTHER
}

enum OrderStatus {
  OPEN
  PAID
  PART_PAID
  CANCELLED
}

enum TenderType {
  CARD
  CASH
  OTHER
}

enum CatalogImageSize {
  THUMBNAIL
  SMALL
  MEDIUM
  LARGE
  NATIVE
}

type TaxAmount {
  name: String!
  amount: Int!
}

type PosReceipt {
  orderUuid: ID!
  orderReferenceNumber: String!
  createdTimestamp: AWSDateTime!
  createdTimestampLocal: AWSDateTime
  paidTimestampUTC: AWSDateTime
  orderStatus: OrderStatus!
  paidAmount: Int!
  currency: String!
  dueAmount: Int!
  totalAmount: Int
    @deprecated(
      reason: "The order total amount is now calculated differently in new releases. Use orderAmount instead."
    )
  orderAmount: Int!
  totalGst: Int!
  orderGst: Int
  totalSurcharge: Int!
  totalDiscount: Int!
  totalServiceCharge: Int!
  totalTipAmount: Int!
  subtotalAmount: Int!
  totalChange: Int
  totalAmountTendered: Int
  cashRoundingAdjustment: Int
  taxInvoice: Boolean!
  orderPayments: [OrderPayment!]
  items: [TransactionItem!]
  orderDiscounts: [TransactionDiscount!]
  orderServiceCharges: [TransactionServiceCharge!]
  catalogSettings: CatalogSettingsSnapshot
  #settings same as previous
  businessName: String
  siteName: String
  address: SiteAddress
  abn: String
  acn: String
  email: String
  website: String
  phone: String
  logo: String
  returnMessage: String
  message: String
  twitter: String
  facebook: String
  instagram: String
  linkedin: String
}
type OrderPayment {
  id: String!
  orderPaymentShortId: String!
  note: String
  tenderType: TenderType!
  tenderSubType: String
  timestamp: AWSDateTime!
  timestampLocal: AWSDateTime!
  amount: Int!
  taxAmounts: [TaxAmount!]!
  gst: Int!
  surchargeAmount: Int!
  tips: Int!
  amountTendered: Int!
  change: Int!
  status: TransactionStatus
  transactionReceiptUrl: String
  cardDetails: CardDetails
}

type CardDetails {
  transactionUuid: String
  scheme: CardScheme!
  maskedPan: String!
  cardMedia: CardMedia
  emvAid: String
  emvTagsPrint: String
  emvAppName: String
  account: CardApplicationAccount
  catid: String
}

type OrderTransaction {
  transactionUuid: String!
  timestamp: String!
  timestampLocal: String!
  type: ReceiptType!
  reference: String
  rrn: String
  status: TransactionStatus!
  scheme: CardScheme!
  maskedPan: String!
  cardMedia: CardMedia
  emvAid: String
  emvTagsPrint: String
  emvAppName: String
  account: CardApplicationAccount
  cardholderUuid: String
  catid: String
}
