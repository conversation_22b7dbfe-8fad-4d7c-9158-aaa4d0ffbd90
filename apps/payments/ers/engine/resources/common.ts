import { ApiAppEnvConfig } from '@npco/component-bff-serverless';
import { Package } from 'serverless/aws';
import { ServerlessPlugin } from '@npco/component-bff-serverless';

export const pluginsResolverOnly = [
  ServerlessPlugin.Tracing,
  ServerlessPlugin.ResourceTagging,
  ServerlessPlugin.EsBuild,
  ServerlessPlugin.Dotenv,
];

export const pluginsApp = [...pluginsResolverOnly];
export const esbuild = {
  bundle: true,
  keepNames: true,
  exclude: [
    'cache-manager',
    'class-transformer',
    'class-validator',
    '@nestjs/platform-express',
    '@nestjs/microservices',
    '@nestjs/websockets/socket-module',
  ],
  plugins: 'esbuild_plugin.js',
};

export const esbuildWithExternal = {
  ...esbuild,
  external: ['puppeteer-core', '@sparticuz/chromium'],
};

export const prune = {
  automatic: true,
  includeLayers: true,
  number: 5,
};

class ApiAppWithFontsEnvConfig extends ApiAppEnvConfig {
  readonly package: Package = {
    patterns: ['!node_modules/**', 'fonts/**'],
  };
}
export const envConfig = new ApiAppWithFontsEnvConfig();

export const getVpcEndpointIds = () => {
  const stage = process.env.STAGE || '';
  const vpcVpcEndpointIds = ['${ssm:${env:VPC_ENV_NAME}-ers-api-gateway-endpoint}'];
  if (!(stage.startsWith('st') && stage !== 'staging')) {
    vpcVpcEndpointIds.push('${ssm:${env:VPC_ENV_NAME}-mp-api-gateway-endpoint}');
    vpcVpcEndpointIds.push('${ssm:${env:VPC_ENV_NAME}-ams-api-gateway-endpoint}');
  }
  return vpcVpcEndpointIds;
};

export const getEntityTableName = () => {
  return '${env:STATIC_ENV_NAME}-mp-api-dynamodb-Entities';
};

export const getEntityTableArn = () => {
  return `arn:aws:dynamodb:\${self:provider.region}:\${self:custom.accountId}:table/${getEntityTableName()}`;
};
export const getOrdersTableName = () => {
  return '${env:STATIC_ENV_NAME}-mp-api-dynamodb-Orders';
};
export const getCatalogsTableName = () => {
  return '${env:STATIC_ENV_NAME}-mp-api-dynamodb-Catalogs';
};

export const getOrdersTableArn = () => {
  return `arn:aws:dynamodb:\${self:provider.region}:\${self:custom.accountId}:table/${getOrdersTableName()}`;
};
