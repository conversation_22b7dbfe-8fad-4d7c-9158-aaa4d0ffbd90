import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { isSystemTest } from './envUtils';

@Injectable()
export class EnvironmentService {
  componentName: string;

  stage: string;

  region: string;

  isInLambda: boolean;

  assetsBaseUrl: string;

  receiptEmailSubject: string;

  receiptSourceEmail: string;

  optOutReceiptsUrl: string;

  amsEndpointVersion: string;

  amsEndpoint: string;

  smsReceiptStorage: string;

  cqrsCommandHandler: string;

  smsBaseUrl: string;

  posReceiptBaseUrl: string;

  pdfReceiptBaseUrl: string;

  // message media feature flag
  isMessageMediaEnabled: boolean;

  messageMediaApiKeySsmName: string;

  messageMediaApiSecretSsmName: string;

  ordersTableName: string;

  catalogsTableName: string;

  isSystemTest: boolean;

  dashboardUrl: string;

  // cmd handler lambda
  lambdaHttpTimeout = 5000;

  lambdaConnectTimeout = 2000;

  maxRetries = 3;

  constructor(private readonly configService: ConfigService) {
    this.componentName = this.configService.get('COMPONENT_NAME', '');
    this.stage = this.configService.get('STAGE', '');
    this.region = this.configService.get('AWS_REGION', '');
    this.isInLambda = !!this.configService.get('LAMBDA_TASK_ROOT', '');
    this.receiptSourceEmail = this.configService.get('RECEIPT_SOURCE_EMAIL', '<EMAIL>');
    this.receiptEmailSubject = this.configService.get('RECEIPT_EMAIL_SUBJECT', 'Receipt <NUM> from Zeller');
    this.optOutReceiptsUrl = this.configService.get('OPT_OUT_RECEIPTS_BASE_URL', '');
    this.assetsBaseUrl = this.configService.get('RECEIPT_HTML_ASSETS_URL', '');
    this.amsEndpoint = this.configService.get('AMS_API_ENDPOINT', '');
    this.amsEndpointVersion = this.configService.get('AMS_API_ENDPOINT_VERSION', 'v1');
    this.smsReceiptStorage = this.configService.get('S3_RECEIPTS_BUCKET', 'receipts');
    this.cqrsCommandHandler = this.configService.get('CMD_ERS_CQRS', '');
    this.smsBaseUrl = this.configService.get('SMS_RECEIPT_URL', 'https://services.myzeller.dev/member/receipts');
    this.posReceiptBaseUrl = this.configService.get(
      'POS_RECEIPT_URL_NAME',
      'https://services.myzeller.dev/member/pos-receipt/',
    );
    this.pdfReceiptBaseUrl = this.configService.get('PDF_RECEIPT_URL', 'https://pay.myzeller.com/');
    this.isMessageMediaEnabled = this.configService.get('MESSAGE_MEDIA_ENABLED', 'false') === 'true';
    this.messageMediaApiKeySsmName = this.configService.get('MESSAGE_MEDIA_API_KEY_SSM_NAME', '');
    this.messageMediaApiSecretSsmName = this.configService.get('MESSAGE_MEDIA_API_SECRET_SSM_NAME', '');
    this.ordersTableName = this.configService.get('ORDERS_TABLE', '');
    this.catalogsTableName = this.configService.get('CATALOGS_TABLE', '');
    this.isSystemTest = isSystemTest(this.stage);
    this.dashboardUrl = this.configService.get('DASHBOARD_BASE_URL', 'https://dashboard.myzeller.dev');
  }
}
