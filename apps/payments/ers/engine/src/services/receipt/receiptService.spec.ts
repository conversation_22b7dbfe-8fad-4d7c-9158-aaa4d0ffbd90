import { NotFoundError } from '@npco/component-bff-core/dist/error';
import type { CatalogItem } from '@npco/component-dto-catalog/dist';
import { ISO4217, Source, TransactionType } from '@npco/component-dto-core/dist/';
import { OrderPaymentStatus, TenderType, OrderItemType, OrderStatus } from '@npco/component-dto-order/dist';
import type { Order, OrderPayment } from '@npco/component-dto-order/dist/';
import { TransactionStatus } from '@npco/component-dto-transaction/dist/';

import { S3 } from '@aws-sdk/client-s3';
import { v4 } from 'uuid';

import { ReceiptStorage } from '../storage/receiptStorage';

import { ReceiptModelV2 } from './model/receiptModelV2';
import { ReceiptService } from './receiptService';
import { FileFormat } from './types';

const mockBuildEmail = jest.fn();
jest.mock('@npco/zeller-react-emails', () => {
  const service = {
    buildEmail: (...args: any[]) => mockBuildEmail(...args),
  };
  return {
    TemplateClassMap: {
      TRANSACTION_RECEIPT_TEMPLATE: jest.fn(() => service),
      POS_RECEIPT_TEMPLATE: jest.fn(() => service),
    },
    TemplateNameMap: {
      TRANSACTION_RECEIPT_TEMPLATE: 'TRANSACTION_RECEIPT_TEMPLATE',
      POS_RECEIPT_TEMPLATE: 'POS_RECEIPT_TEMPLATE',
    },
  };
});

jest.mock('@npco/receipt', () => {
  return {
    renderPosReceiptHtml: jest.fn(),
    renderReceiptHtml: jest.fn(),
    transformGetPosReceiptResponse: jest.fn(),
    transformGetReceiptResponse: jest.fn(),
  };
});

const entity = {
  id: v4(),
  abn: v4().split('-').at(-1),
};

export const txn = {
  id: 'uuid',
  siteUuid: v4(),
  cardholderUuid: v4(),
  entityUuid: entity.id,
  rrn: v4(),
  timestampLocal: '2020-12-16T14:17:59+03:00',
  timestamp: '2020-12-16T17:17:59+00:00',
  terminalId: 'dat876587657658765a',
  deviceUuid: v4(),
  taxAmounts: [
    {
      name: 'GST',
      amount: 0,
    },
  ],
  aidLabel: 'data',

  cardType: 'data',

  aid: 'data',

  maskedPan: '…1111',

  saleAmount: 100,

  surchargeAmount: 100,

  currency: ISO4217.AUD,
  tipAmount: 100,
  amount: 100,

  status: TransactionStatus.APPROVED,

  reference: '#1444',
  externalReference: 'INV0001',

  authCode: 'data',

  source: Source.STANDALONE,
};

export const receiptSettings = {
  name: 'Customer Name',
  number: '00923',
  facebook: 'facebook://myzeller',
  twitter: '@myzeller',
  phone: '+***********',
  email: '<EMAIL>',
};

const expectedReceipt = {
  transactionUuid: 'uuid',
  timestamp: '2020-12-16T17:17:59+00:00',
  timestampLocal: '2020-12-16T14:17:59+03:00',
  type: 'SALE',
  rrn: '#1444',
  reference: '#1444',
  currency: ISO4217.AUD,
  totalDiscount: 0,
  totalServiceCharge: 0,
  totalAmount: 100,
  totalGst: 0,
  totalSurcharge: 100,
  totalTips: 100,
  subtotalAmount: 100,
  status: 'APPROVED',
  scheme: undefined,
  maskedPan: '1111',
  emvTagsPrint: '',
  taxInvoice: false,
  businessName: 'Customer Name',
  siteName: undefined,
  cardMedia: undefined,
  catid: undefined,
  address: undefined,
  abn: entity.abn,
  email: '<EMAIL>',
  website: undefined,
  phone: '+***********',
  logo: undefined,
  returnMessage: undefined,
  message: undefined,
  twitter: '@myzeller',
  facebook: 'facebook://myzeller',
  instagram: undefined,
  linkedin: undefined,
  emvAppName: '',
  emvAid: '',
  cardholderUuid: txn.cardholderUuid,
};

export const mockConvertDbItemToTransaction = jest.fn().mockReturnValue(txn);

jest.mock('@npco/component-dbs-mp-common/dist', () => ({
  convertDbItemToTransaction: () => mockConvertDbItemToTransaction(),
}));
const mockGetSiteDbItem = jest.fn().mockReturnValue({ receipt: receiptSettings });
const mockConvertDbItemToSite = jest.fn().mockReturnValue({ receipt: receiptSettings });

jest.mock('@npco/component-dbs-mp-common/dist/site/getSiteDbItem', () => ({
  getSiteDbItem: () => mockGetSiteDbItem(),
  convertDbItemToSite: () => mockConvertDbItemToSite(),
}));
const mockGetEntityDbItem = jest.fn().mockReturnValue(entity);

jest.mock('@npco/component-dbs-mp-common/dist/entity/getEntityDbItem', () => ({
  getEntityDbItem: () => mockGetEntityDbItem(),
}));
const mockGetTransactionDbItem = jest.fn().mockReturnValue(txn);

jest.mock('@npco/component-dbs-mp-common/dist/transaction/getTransactionDbItem', () => ({
  getTransactionDbItemOrThrow: () => mockGetTransactionDbItem(),
}));

const order: Order = {
  id: v4(),
  entityUuid: entity.id,
  referenceNumber: v4(),
  siteUuid: v4(),
  createdTime: 1721116178,
  createdTimestampLocal: '2024-07-16T07:49:38.000+05:30',
  paidTime: 1721116178,
  status: OrderStatus.PAID,
  items: [
    {
      id: v4(),
      name: v4(),
      ordinal: 1,
      type: OrderItemType.SINGLE,
      price: {
        currency: ISO4217.AUD,
        value: '5035',
      },
      quantity: 1,
      unit: 'QUANTITY' as any,
      taxes: [
        {
          enabled: false,
          name: 'GST',
          percent: 10,
        },
      ],
      discounts: [
        {
          id: v4(),
          discountedAmount: {
            currency: ISO4217.AUD,
            value: '1000',
          },
          config: 'PERCENTAGE' as any,
          ordinal: 1,
          value: '10',
          name: '10%',
        },
      ],
      serviceCharges: [
        {
          id: v4(),
          serviceChargeAmount: {
            currency: ISO4217.AUD,
            value: '1000',
          },
          config: 'PERCENTAGE' as any,
          ordinal: 1,
          value: '10',
          name: '10%',
        },
      ],
    },
    {
      id: v4(),
      name: v4(),
      ordinal: 1,
      type: OrderItemType.VARIANT,
      price: {
        currency: ISO4217.AUD,
        value: '5035',
      },
      subtotalAmount: {
        currency: ISO4217.AUD,
        value: '5035',
      },
      totalAmount: {
        currency: ISO4217.AUD,
        value: '5035',
      },
      variantName: '200g',
      quantity: 1,
      unit: 'QUANTITY' as any,
      taxes: [
        {
          enabled: false,
          name: 'GST',
          percent: 10,
        },
      ],
      discounts: [],
      serviceCharges: [],
    },
  ],
  discounts: [
    {
      id: v4(),
      discountedAmount: {
        currency: ISO4217.AUD,
        value: '1000',
      },
      config: 'PERCENTAGE' as any,
      ordinal: 1,
      value: '10',
      name: '10%',
    },
  ],
  serviceCharges: [
    {
      id: v4(),
      serviceChargeAmount: {
        currency: ISO4217.AUD,
        value: '1000',
      },
      config: 'PERCENTAGE' as any,
      ordinal: 1,
      value: '10',
      name: '10%',
    },
  ],
  payments: [
    {
      id: v4(),
      entityUuid: txn.entityUuid,
      type: TransactionType.PURCHASE,
      status: OrderPaymentStatus.APPROVED,
      tenderType: TenderType.CARD,
      timestamp: '2024-12-11T02:33:05.731Z',
      transactionUuid: txn.id,
      amount: {
        currency: ISO4217.AUD,
        value: '10000',
      },
      amountTendered: {
        currency: ISO4217.AUD,
        value: '1000',
      },
      change: {
        currency: ISO4217.AUD,
        value: '2000',
      },
      taxAmounts: [
        {
          name: 'GST',
          amount: 100,
        },
      ],
      surchargeAmount: {
        currency: ISO4217.AUD,
        value: '10000',
      },
      tips: {
        currency: ISO4217.AUD,
        value: '10000',
      },
    } as OrderPayment,
  ],
};

const expectedPosReceipt = {
  abn: entity.abn,
  businessName: 'Customer Name',
  createdTimestamp: '2024-07-16T07:49:38.000Z',
  createdTimestampLocal: '2024-07-16T07:49:38.000+05:30',
  paidTimestampUTC: '2024-07-16T07:49:38.000Z',
  currency: 'AUD',
  dueAmount: 0,
  orderAmount: 0,
  email: '<EMAIL>',
  facebook: 'facebook://myzeller',
  items: [
    {
      discounts: [
        {
          config: 'PERCENTAGE',
          discountedAmount: '1000',
          id: order.items![0].discounts![0].id,
          name: '10%',
          ordinal: 1,
          value: '10',
        },
      ],
      serviceCharges: [
        {
          config: 'PERCENTAGE',
          serviceChargeAmount: '1000',
          id: order.items![0].serviceCharges![0].id,
          name: '10%',
          ordinal: 1,
          value: '10',
        },
      ],

      id: order.items![0].id,
      name: order.items![0].name,
      ordinal: 1,
      price: '5035',
      quantity: 1,
      taxes: [
        {
          enabled: false,
          name: 'GST',
          percent: 10,
        },
      ],
      type: 'SINGLE',
      unit: 'QUANTITY',
    },
    {
      discounts: [],
      serviceCharges: [],
      id: order.items![1].id,
      name: order.items![1].name,
      ordinal: 1,
      price: '5035',
      quantity: 1,
      subtotalAmount: '5035',
      taxes: [
        {
          enabled: false,
          name: 'GST',
          percent: 10,
        },
      ],
      totalAmount: '5035',
      type: 'VARIANT',
      unit: 'QUANTITY',
      variantName: '200g',
    },
  ],
  orderDiscounts: [
    {
      config: 'PERCENTAGE',
      discountedAmount: '1000',
      id: order.discounts![0].id,
      name: '10%',
      ordinal: 1,
      value: '10',
    },
  ],
  orderServiceCharges: [
    {
      config: 'PERCENTAGE',
      serviceChargeAmount: '1000',
      id: order.serviceCharges![0].id,
      name: '10%',
      ordinal: 1,
      value: '10',
    },
  ],
  orderPayments: [
    {
      amount: 100,
      amountTendered: 10,
      change: 20,
      gst: 0,
      id: order.payments![0].id,
      orderPaymentShortId: '#1444',
      status: 'APPROVED',
      surchargeAmount: 100,
      timestampLocal: '2020-12-16T14:17:59+03:00',
      timestamp: '2020-12-16T17:17:59+00:00',
      tips: 100,
      taxAmounts: [
        {
          name: 'GST',
          amount: 0,
        },
      ],
      type: 'PURCHASE',
      tenderType: 'CARD',
      transactionReceiptUrl: `http://myzeller.dev/service/${txn.id}`,
      cardDetails: {
        cardholderUuid: txn.cardholderUuid,
        emvAid: '',
        emvAppName: '',
        emvTagsPrint: '',
        id: txn.id,
        maskedPan: '1111',
      },
    },
  ],
  orderReferenceNumber: order.referenceNumber,
  orderStatus: 'PAID',
  orderUuid: order.id,
  paidAmount: 0,
  phone: '+***********',
  subtotalAmount: 0,
  taxInvoice: false,
  totalAmount: 0,
  totalDiscount: 0,
  totalServiceCharge: 0,
  totalGst: 0,
  orderGst: 0,
  totalSurcharge: 0,
  totalTipAmount: 0,
  cashRoundingAdjustment: 0,
  twitter: '@myzeller',
};

const catalogItem = {
  id: v4(),
  entityUuid: entity.id,
  amount: 1000,
  currency: ISO4217.AUD,
  name: 'Test Catalog Item',
  description: 'This is a test catalog item',
  reportingCategoryColor: '#FF5733',
  images: [
    {
      id: v4(),
      sizes: [
        {
          size: 'SMALL', // Assuming CatalogImageSize.SMALL is a string
          url: 'https://example.com/image-small.jpg',
        },
      ],
    },
  ],
} as CatalogItem;

const mockOrderRepository = {
  getOrder: jest.fn().mockResolvedValue(order),
  getOrderByIdOrThrowNotFound: jest.fn().mockResolvedValue(order),
};

const mockCatalogRepository = {
  getCatalogItem: jest.fn().mockResolvedValue(catalogItem),
};

jest.mock('@aws-sdk/client-s3', () => {
  const mS3 = {
    putObject: jest.fn().mockReturnThis(),
  };
  return { S3: jest.fn(() => mS3) };
});

const env = {
  pdfReceiptBaseUrl: 'url/',
  posReceiptBaseUrl: 'pos-receipt/url/',
  smsReceiptStorage: 'smsReceiptStorageBucket',
  smsBaseUrl: 'http://myzeller.dev/service/',
};

jest.mock('./htmlConverter/generateFileFromHtml', () => ({
  generatePdfFromHtml: jest.fn().mockResolvedValue(Buffer.from('')),
  generatePngFromHtml: jest.fn().mockResolvedValue(Buffer.from('')),
}));

describe('receiptServiceV2', () => {
  const mockDynamodbService = {
    queryOneByTypeWithID: jest.fn(),
    queryIdByType: jest.fn(),
  };
  let receiptServiceV2: ReceiptService;
  const mockPutObject = jest.fn();

  beforeEach(() => {
    (S3 as any).mockImplementation(() => ({
      putObject: mockPutObject,
    }));
    receiptServiceV2 = new ReceiptService(
      mockDynamodbService as any,
      {} as any,
      env as any,
      new ReceiptStorage(env as any),
      new ReceiptModelV2(),
      mockOrderRepository as any,
      mockCatalogRepository as any,
    );
    mockPutObject.mockReset();
    mockPutObject.mockReturnValue({});
  });

  describe('getReceipt', () => {
    it('should get a receipt', async () => {
      const receipt = await receiptServiceV2.getReceipt({ transactionUuid: 'uuid' });
      expect(receipt).toEqual({ ...expectedReceipt, saleAmount: 100 });
    });

    it('should throw 404 for transaction status other than APPROVED and DECLINED', async () => {
      mockConvertDbItemToTransaction.mockReturnValueOnce({ ...txn, status: 'PENDING' });
      await expect(receiptServiceV2.getReceipt({ transactionUuid: 'uuid' })).rejects.toThrow('Transaction not found');
    });

    it('should throw 404 for site that does not exist', async () => {
      mockGetSiteDbItem.mockResolvedValueOnce(undefined);
      await expect(receiptServiceV2.getReceipt({ transactionUuid: 'uuid' })).rejects.toThrow(
        new NotFoundError(`Site ${txn.siteUuid} not found`),
      );
    });

    it('should throw 404 for entity that does not exist', async () => {
      mockGetEntityDbItem.mockResolvedValueOnce(undefined);
      await expect(receiptServiceV2.getReceipt({ transactionUuid: 'uuid' })).rejects.toThrow(
        new NotFoundError(`Entity ${txn.entityUuid} not found`),
      );
    });
  });

  describe('getPosReceipt', () => {
    it('should get a POS receipt', async () => {
      const receipt = await receiptServiceV2.getPosReceipt({ orderUuid: 'uuid' });
      expect(JSON.parse(JSON.stringify(receipt))).toEqual(expectedPosReceipt);
    });

    it('should throw 404 for transaction status other than APPROVED and DECLINED', async () => {
      mockConvertDbItemToTransaction.mockReturnValueOnce({ ...txn, status: 'PENDING' });
      await expect(receiptServiceV2.getPosReceipt({ orderUuid: 'uuid' })).rejects.toThrow('Transaction not found');
    });

    it('should throw 404 for site that does not exist', async () => {
      mockGetSiteDbItem.mockResolvedValueOnce(undefined);
      await expect(receiptServiceV2.getPosReceipt({ orderUuid: 'uuid' })).rejects.toThrow(
        new NotFoundError(`Site ${order.siteUuid} not found`),
      );
    });

    it('should throw 404 for entity that does not exist', async () => {
      mockGetEntityDbItem.mockResolvedValueOnce(undefined);
      await expect(receiptServiceV2.getPosReceipt({ orderUuid: 'uuid' })).rejects.toThrow(
        new NotFoundError(`Entity ${txn.entityUuid} not found`),
      );
    });
  });

  it('should convert number', () => {
    expect(receiptServiceV2.numberToString(null)).toBe(null);
  });

  it('should get a receipt with items, discounts and serviceCharges when source = ZELLER_POS and external reference set', async () => {
    txn.source = Source.ZELLER_POS;
    txn.externalReference = v4();

    const { convertMoneyType } = new ReceiptModelV2();

    const receipt = await receiptServiceV2.getReceipt({ transactionUuid: 'uuid' });
    expect(receipt).toEqual({
      ...expectedReceipt,
      account: undefined,
      acn: undefined,
      subtotalAmount: 0,
      saleAmount: 100,
      orderReferenceNumber: order.referenceNumber,
      orderReceiptUrl: `${env.posReceiptBaseUrl}${order.id}`,
      items: order?.items?.map((item) => ({
        ...item,
        subtotalAmount: undefined,
        totalAmount: undefined,
        modifiers: item.modifiers?.map((modifier) => ({
          ...modifier,
          price: convertMoneyType(modifier.price),
        })),
        discounts: item.discounts?.map((discount) => ({
          ...discount,
          discountedAmount: convertMoneyType(discount.discountedAmount),
        })),
        serviceCharges: item.serviceCharges?.map((serviceCharge) => ({
          ...serviceCharge,
          serviceChargeAmount: convertMoneyType(serviceCharge.serviceChargeAmount),
        })),
        price: convertMoneyType(item.price),
        ...(item.totalAmount && { totalAmount: convertMoneyType(item.totalAmount) }),
        ...(item.subtotalAmount && { subtotalAmount: convertMoneyType(item.subtotalAmount) }),
      })),
      orderDiscounts: order.discounts?.map((discount) => ({
        ...discount,
        discountedAmount: convertMoneyType(discount.discountedAmount),
      })),
      orderServiceCharges: order.serviceCharges?.map((serviceCharge) => ({
        ...serviceCharge,
        serviceChargeAmount: convertMoneyType(serviceCharge.serviceChargeAmount),
      })),
    });
  });

  it('should getReceiptDownloadUrl when fileFormat is PDF', async () => {
    const transactionUuid = v4();
    const response = await receiptServiceV2.getReceiptDownloadUrl({ transactionUuid, format: FileFormat.PDF });
    expect(response.downloadLink).toBe(`url/pdf/receipt-${transactionUuid}.pdf`);
    expect(response.expire).toBeDefined();

    expect(mockPutObject).toHaveBeenCalledTimes(1);
  });

  it('should getReceiptDownloadUrl when fileFormat is PNG', async () => {
    const transactionUuid = v4();
    const response = await receiptServiceV2.getReceiptDownloadUrl({ transactionUuid, format: FileFormat.PNG });
    expect(response.downloadLink).toBe(`url/png/receipt-${transactionUuid}.png`);
    expect(response.expire).toBeDefined();

    expect(mockPutObject).toHaveBeenCalledTimes(1);
  });

  it('should getPosReceiptDownloadUrl when fileFormat is PDF', async () => {
    const orderUuid = v4();
    const response = await receiptServiceV2.getPosReceiptDownloadUrl({ orderUuid, format: FileFormat.PDF });
    expect(response.downloadLink).toBe(`url/pdf/pos-receipt-${orderUuid}.pdf`);
    expect(response.expire).toBeDefined();

    expect(mockPutObject).toHaveBeenCalledTimes(1);
  });

  it('should getReceiptDownloadUrl when fileFormat is PNG', async () => {
    const orderUuid = v4();
    const response = await receiptServiceV2.getPosReceiptDownloadUrl({ orderUuid, format: FileFormat.PNG });
    expect(response.downloadLink).toBe(`url/png/pos-receipt-${orderUuid}.png`);
    expect(response.expire).toBeDefined();

    expect(mockPutObject).toHaveBeenCalledTimes(1);
  });
});
