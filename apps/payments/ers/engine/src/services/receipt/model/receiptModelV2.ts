import type { Entity } from '@npco/component-dbs-mp-common/dist/types';
import type { CatalogImage, CatalogItem } from '@npco/component-dto-catalog/dist';
import type { Money } from '@npco/component-dto-core/dist';
import { ISO4217, Source, TransactionType } from '@npco/component-dto-core/dist';
import type { Order, OrderDiscount, OrderItem, OrderServiceCharge } from '@npco/component-dto-order/dist';
import { TenderType } from '@npco/component-dto-order/dist';
import type { Transaction } from '@npco/component-dto-transaction/dist';

import type { EnvironmentService } from '../../../config';
import { emvReader, readEmvLine1Parts } from '../emvReader';
import type {
  Receipt,
  ReceiptOrderDiscount,
  ReceiptOrderItem,
  ReceiptOrderServiceCharge,
  SiteWithReceiptSettings,
} from '../types';
import { CardApplicationAccount, ReceiptType } from '../types';

import { BaseReceiptModelV2 } from './baseReceiptModelV2';

export class ReceiptModelV2 extends BaseReceiptModelV2 {
  readonly getGstValue = (transaction: { taxAmounts?: { name: string; amount: number }[] }): number => {
    return transaction.taxAmounts?.filter((tax) => tax.name === 'GST').reduce((acc, tax) => acc + tax.amount, 0) ?? 0;
  };

  readonly getReceiptType = (transaction: Transaction): ReceiptType => {
    const { type, source } = transaction;

    if (type === TransactionType.REFUND) {
      return ReceiptType.REFUND;
    }

    if ([Source.ZELLER_INVOICE, Source.XERO_INVOICE].includes(source)) {
      return ReceiptType.INVOICE;
    }

    if ([Source.VIRTUAL_TERMINAL, Source.PAY_BY_LINK].includes(source)) {
      return ReceiptType.PAYMENT;
    }
    return ReceiptType.SALE;
  };

  readonly getReference = (transaction: Transaction, type: ReceiptType): string => {
    let reference;
    switch (type) {
      case ReceiptType.REFUND:
        reference = transaction.reference;
        break;
      case ReceiptType.INVOICE:
        reference = transaction.externalReference;
        break;
      case ReceiptType.PAYMENT:
        reference = transaction.externalReference;
        break;
      case ReceiptType.SALE:
        reference = transaction.reference;
        break;
      default:
        reference = transaction.reference;
        break;
    }
    return reference ?? '';
  };

  getCardApplicationAccount = (transaction: Transaction): CardApplicationAccount | undefined => {
    const applicationCode = transaction.isoProcessingCode ? transaction.isoProcessingCode.slice(2, 4) : undefined;
    switch (applicationCode) {
      case '10':
        return CardApplicationAccount.SAVINGS;
      case '20':
        return CardApplicationAccount.CHEQUE;
      case '30':
        return CardApplicationAccount.CREDIT;
      default:
        return undefined;
    }
  };

  /**
   * The sale amount is less than $1,000
   * The sale includes GST
   * The sale has itemised details
   * The entity has an ABN (Australian Business Number). Only an ACN is not sufficient.
   */
  getIsOrderTaxInvoice = (entity: Entity, order: Order): boolean => {
    return (
      Number(order?.totalAmount?.value) < ******** &&
      Number(order.totalGst?.value) > 0 &&
      Number(order?.items?.length) > 0 &&
      !!entity.abn
    );
  };

  readonly convertMoneyType = (money?: Money): string => {
    return money?.value ?? '0';
  };

  readonly formatMaskedPan = (maskedPan: string): string => {
    return maskedPan.startsWith('…') ? maskedPan.replace('…', '') : maskedPan;
  };

  formatReceiptOrderDiscounts = (orderDiscounts?: OrderDiscount[]): ReceiptOrderDiscount[] | undefined => {
    if (!orderDiscounts) {
      return undefined;
    }

    return orderDiscounts.map((discount) => ({
      ...discount,
      discountedAmount: this.convertMoneyType(discount.discountedAmount),
    }));
  };

  formatReceiptOrderServiceCharges = (
    orderServiceCharges?: OrderServiceCharge[],
  ): ReceiptOrderServiceCharge[] | undefined => {
    if (!orderServiceCharges) return undefined;

    return orderServiceCharges.map((serviceCharge) => ({
      ...serviceCharge,
      serviceChargeAmount: this.convertMoneyType(serviceCharge.serviceChargeAmount),
    }));
  };

  formatReceiptOrderItems = (items?: OrderItem[], catalogItems?: CatalogItem[]): ReceiptOrderItem[] | undefined => {
    if (!items) {
      return undefined;
    }

    const catalogItemToOrderMap = catalogItems?.length
      ? catalogItems?.reduce((acc, item) => {
          acc[item.id] = item;
          return acc;
        }, {} as Record<string, CatalogItem>)
      : {};

    return items.map((item) => {
      const mappedItem: ReceiptOrderItem = {
        ...item,
        modifiers: item.modifiers?.map((modifier) => ({
          ...modifier,
          price: this.convertMoneyType(modifier.price),
          subtotalAmount: this.convertMoneyType(modifier.subtotalAmount),
        })),
        discounts: item.discounts?.map((discount) => ({
          ...discount,
          discountedAmount: this.convertMoneyType(discount.discountedAmount),
        })),
        serviceCharges: item.serviceCharges?.map((serviceCharge) => ({
          ...serviceCharge,
          serviceChargeAmount: this.convertMoneyType(serviceCharge.serviceChargeAmount),
        })),
        price: this.convertMoneyType(item.price),
        ...((item.catalogItem?.id as string) in catalogItemToOrderMap && {
          reportingCategoryColor: catalogItemToOrderMap[item.catalogItem?.id as string]?.reportingCategoryColor,
          images: catalogItemToOrderMap[item.catalogItem?.id as string]?.images?.map((image) => ({
            id: image.id,
            sizes: image.sizes,
          })) as Pick<CatalogImage, 'id' | 'sizes'>[],
        }),
        subtotalAmount: undefined,
        totalAmount: undefined,
      };

      if (item.subtotalAmount) {
        mappedItem.subtotalAmount = this.convertMoneyType(item.subtotalAmount);
      }
      if (item.totalAmount) {
        mappedItem.totalAmount = this.convertMoneyType(item.totalAmount);
      }

      return mappedItem;
    });
  };

  toNumber = (value: string | number | undefined): number =>
    value && !Number.isNaN(Number(value)) ? Number(value) : 0;

  convertCentiCentsToCents = (amount: string | number | undefined): number => {
    const centiCentToCentRate = 100;
    return Number(Number(this.toNumber(amount) / centiCentToCentRate).toFixed(0));
  };

  getTotalDiscount = (order?: Order): number => {
    const amount = this.convertMoneyType(order?.totalDiscount);
    return this.convertCentiCentsToCents(amount);
  };

  getTotalServiceCharge = (order?: Order): number => {
    const amount = this.convertMoneyType(order?.totalServiceCharge);
    return this.convertCentiCentsToCents(amount);
  };

  getSubtotalAmount = (transaction: Transaction, order?: Order): number => {
    if (order) {
      const amount = this.convertMoneyType(order?.subtotalAmount);
      return this.convertCentiCentsToCents(amount);
    }
    return transaction.saleAmount;
  };

  getOrderDataInCents = (data?: Money) => this.convertCentiCentsToCents(this.convertMoneyType(data));

  formatOrderPayments = (order: Order, transactions: Transaction[], envService: EnvironmentService) => {
    return (order.payments ?? []).map((payment) => {
      const transaction = transactions.find((t) => t.id === payment.transactionUuid);

      const orderPayment = {
        id: payment.id,
        orderPaymentShortId: payment.shortId,
        type: payment.type,
        tenderType: payment.tenderType ?? TenderType.CARD,
        tenderSubType: payment.tenderSubType,
        amount: this.getOrderDataInCents(payment.amount),
        amountTendered: this.getOrderDataInCents(payment.amountTendered),
        change: this.getOrderDataInCents(payment.change),
        gst: this.getGstValue(payment),
        taxAmounts: payment.taxAmounts,
        surchargeAmount: this.getOrderDataInCents(payment.surchargeAmount),
        tips: this.getOrderDataInCents(payment.tips),
        status: payment.status,
        timestamp: payment.timestamp,
        timestampLocal: payment.timestampLocal,
      };
      if (transaction) {
        const { emvAid, emvAppName } = readEmvLine1Parts(transaction as any);
        const emvTagsPrint = emvReader(transaction as any, 2);
        orderPayment.timestamp = transaction.timestamp;
        orderPayment.timestampLocal = transaction.timestampLocal;
        orderPayment.orderPaymentShortId = transaction.reference;
        orderPayment.gst = this.getGstValue(transaction);
        orderPayment.taxAmounts = transaction.taxAmounts;
        (orderPayment as any).transactionReceiptUrl = `${envService.smsBaseUrl}${transaction.id}`;
        (orderPayment as any).cardDetails = {
          id: transaction.id,
          scheme: transaction.scheme,
          maskedPan: transaction.maskedPan ? this.formatMaskedPan(transaction.maskedPan) : '',
          cardMedia: transaction.cardMedia,
          emvAid,
          emvTagsPrint,
          emvAppName,
          catid: transaction.catid,
          cardholderUuid: transaction.cardholderUuid,
          account: this.getCardApplicationAccount(transaction),
        };
      }
      return orderPayment;
    });
  };

  getReceipt = (
    transaction: Transaction,
    site: SiteWithReceiptSettings,
    entity: Entity,
    envService: EnvironmentService,
    order?: Order,
  ): Receipt => {
    const receiptSettings = site.receipt;
    const { emvAid, emvAppName } = readEmvLine1Parts(transaction as any);
    const emvTagsPrint = emvReader(transaction as any, 2);
    const type = this.getReceiptType(transaction);
    const reference = this.getReference(transaction, type);
    return {
      transactionUuid: transaction.id,
      timestamp: transaction.timestamp,
      timestampLocal: transaction.timestampLocal,
      type,
      reference,
      rrn: transaction.reference,
      currency: transaction.currency,
      totalDiscount: this.getTotalDiscount(order),
      totalServiceCharge: this.getTotalServiceCharge(order),
      totalAmount: transaction.amount,
      totalGst: this.getGstValue(transaction),
      totalSurcharge: transaction.surchargeAmount,
      totalTips: transaction.tipAmount,
      /**
       * subtotalAmount is deprecated, use saleAmount instead.
       */
      subtotalAmount: this.getSubtotalAmount(transaction, order),
      saleAmount: transaction.saleAmount,
      status: transaction.status,
      scheme: transaction.scheme,
      maskedPan: transaction.maskedPan ? this.formatMaskedPan(transaction.maskedPan) : '',
      cardMedia: transaction.cardMedia,
      emvAid,
      emvTagsPrint,
      emvAppName,
      taxInvoice: false,
      orderReferenceNumber: order?.referenceNumber,
      orderReceiptUrl: order?.id ? `${envService.posReceiptBaseUrl}${order.id}` : undefined,
      items: this.formatReceiptOrderItems(order?.items),
      catalogSettings: order?.catalogSettings,
      orderDiscounts: this.formatReceiptOrderDiscounts(order?.discounts),
      orderServiceCharges: this.formatReceiptOrderServiceCharges(order?.serviceCharges),
      businessName: receiptSettings.name,
      siteName: site.name,
      catid: transaction.catid,
      address: site.address,
      account: this.getCardApplicationAccount(transaction),
      abn: entity.abn,
      acn: entity.acn,
      email: receiptSettings?.email,
      website: receiptSettings?.website,
      phone: receiptSettings?.phone,
      logo: receiptSettings?.logo,
      returnMessage: receiptSettings?.returnsMessage,
      message: receiptSettings?.message,
      twitter: receiptSettings?.twitter,
      facebook: receiptSettings?.facebook,
      instagram: receiptSettings?.instagram,
      cardholderUuid: transaction.cardholderUuid,
    } as any;
  };

  getPosReceipt = (
    order: Order,
    site: SiteWithReceiptSettings,
    entity: Entity,
    transactions: Transaction[],
    catalogItems: CatalogItem[] | undefined,
    envService: EnvironmentService,
  ) => {
    const receiptSettings = site.receipt;
    const posReceipt = {
      orderUuid: order.id,
      orderReferenceNumber: order.referenceNumber,
      createdTimestamp: new Date(order.createdTime * 1000).toISOString(),
      createdTimestampLocal: order.createdTimestampLocal,
      paidTimestampUTC: new Date(order.paidTime! * 1000).toISOString(),
      orderStatus: order.status,
      currency: order.paidAmount?.currency ?? ISO4217.AUD,
      paidAmount: this.getOrderDataInCents(order.paidAmount),
      dueAmount: this.getOrderDataInCents(order.dueAmount),
      totalAmount: this.getOrderDataInCents(order.totalAmount),
      orderAmount: this.getOrderDataInCents(order.orderAmount),
      totalGst: this.getOrderDataInCents(order.totalGst),
      orderGst: this.getOrderDataInCents(order.orderGst),
      totalSurcharge: this.getOrderDataInCents(order.totalSurcharge),
      totalDiscount: this.getOrderDataInCents(order.totalDiscount),
      totalServiceCharge: this.getOrderDataInCents(order.totalServiceCharge),
      totalTipAmount: this.getOrderDataInCents(order.totalTips),
      subtotalAmount: this.getOrderDataInCents(order.subtotalAmount),
      taxInvoice: this.getIsOrderTaxInvoice(entity, order),
      items: this.formatReceiptOrderItems(order?.items, catalogItems),
      catalogSettings: order?.catalogSettings,
      orderDiscounts: this.formatReceiptOrderDiscounts(order?.discounts),
      orderServiceCharges: this.formatReceiptOrderServiceCharges(order?.serviceCharges),
      orderPayments: this.formatOrderPayments(order, transactions, envService),
      cashRoundingAdjustment: this.getOrderDataInCents(order.cashRoundingAdjustment),

      businessName: receiptSettings.name,
      siteName: site.name,
      address: site.address,
      abn: entity.abn,
      acn: entity.acn,
      email: receiptSettings?.email,
      website: receiptSettings?.website,
      phone: receiptSettings?.phone,
      logo: receiptSettings?.logo,
      returnMessage: receiptSettings?.returnsMessage,
      message: receiptSettings?.message,
      twitter: receiptSettings?.twitter,
      facebook: receiptSettings?.facebook,
      instagram: receiptSettings?.instagram,
    };
    return posReceipt as any;
  };
}
