import type { CatalogItem } from '@npco/component-dto-catalog/dist/types';
import { ISO4217, TransactionType } from '@npco/component-dto-core/dist/';
import { OrderPaymentStatus, TenderType } from '@npco/component-dto-order/dist';
import type {
  Order,
  OrderDiscount,
  OrderItem,
  OrderPayment,
  OrderServiceCharge,
} from '@npco/component-dto-order/dist/';
import type { Transaction } from '@npco/component-dto-transaction/dist/';

import { v4 } from 'uuid';

import { CardApplicationAccount } from '../types';

import { ReceiptModelV2 } from './receiptModelV2';

const entity = {
  id: v4(),
  abn: v4().split('-').at(-1),
};

const site = {
  id: v4(),
  receipt: {
    name: 'Customer Name',
    number: entity.abn,
    facebook: 'facebook://myzeller',
    twitter: '@myzeller',
    phone: '+***********',
    email: '<EMAIL>',
  },
};

export const txn = {
  id: 'uuid',
  siteUuid: site.id,
  cardholderUuid: v4(),
  rrn: v4(),
  entityUuid: entity.id,
  timestampLocal: '2020-12-16T14:17:59+03:00',
  timestamp: '2020-12-16T17:17:59+00:00',
  terminalId: 'dat876587657658765a',
  deviceUuid: v4(),
  taxAmounts: [
    {
      name: 'GST',
      amount: 0,
    },
  ],
  aidLabel: 'data',

  cardType: 'data',

  aid: 'data',

  maskedPan: '…1111',

  saleAmount: 100,

  surchargeAmount: 100,

  tipAmount: 100,
  amount: 100,
  currency: ISO4217.AUD,

  status: 'APPROVED',

  reference: '#1444',
  externalReference: 'INV0001',

  authCode: 'data',
};

const expectedReceipt = {
  transactionUuid: 'uuid',
  timestamp: '2020-12-16T17:17:59+00:00',
  timestampLocal: '2020-12-16T14:17:59+03:00',
  type: 'SALE',
  rrn: '#1444',
  reference: '#1444',
  currency: ISO4217.AUD,
  totalDiscount: 0,
  totalServiceCharge: 0,
  totalAmount: 100,
  totalGst: 0,
  totalSurcharge: 100,
  totalTips: 100,
  subtotalAmount: 100,
  saleAmount: 100,
  status: 'APPROVED',
  scheme: undefined,
  maskedPan: '1111',
  cardMedia: undefined,
  emvAid: '',
  emvAppName: '',
  emvTagsPrint: '',
  taxInvoice: false,
  businessName: 'Customer Name',
  siteName: undefined,
  catid: undefined,
  account: undefined,
  address: undefined,
  abn: entity.abn,
  email: '<EMAIL>',
  website: undefined,
  phone: '+***********',
  logo: undefined,
  returnMessage: undefined,
  message: undefined,
  twitter: '@myzeller',
  facebook: 'facebook://myzeller',
  instagram: undefined,
  cardholderUuid: txn.cardholderUuid,
};

const catalogItem = [
  {
    id: v4(),
    entityUuid: entity.id,
    amount: 1000,
    currency: ISO4217.AUD,
    name: 'Test Catalog Item',
    description: 'This is a test catalog item',
    reportingCategoryColor: '#FF5733',
    images: [
      {
        id: v4(),
        sizes: [
          {
            size: 'SMALL', // Assuming CatalogImageSize.SMALL is a string
            url: 'https://example.com/image-small.jpg',
          },
        ],
      },
    ],
  },
] as CatalogItem[];

const order: Order = {
  id: v4(),
  subtotalAmount: { value: '8000000' } as any,
  totalAmount: { value: '8000000' } as any,
  totalGst: { value: '10000' } as any,
  createdTime: 1721116178,
  paidTime: 1721116178,
  createdTimestampLocal: '2024-07-16T07:49:38.000+05:30',
  referenceNumber: 'ZPsxx',
  items: [
    {
      id: v4(),
      name: v4(),
      price: {
        currency: ISO4217.AUD,
        value: '5035',
      },
      modifiers: [
        {
          id: v4(),
          name: v4(),
          price: {
            currency: ISO4217.AUD,
            value: '5035',
          },
        },
      ],
      discounts: [
        {
          id: v4(),
          name: v4(),
          discountedAmount: {
            currency: ISO4217.AUD,
            value: '50',
          },
        } as OrderDiscount,
      ],
      serviceCharges: [
        {
          id: v4(),
          name: v4(),
          serviceChargeAmount: {
            currency: ISO4217.AUD,
            value: '50',
          },
        } as OrderServiceCharge,
      ],
    } as OrderItem,
  ],
  discounts: [
    {
      id: v4(),
      name: v4(),
      discountedAmount: {
        currency: ISO4217.AUD,
        value: '50',
      },
    } as OrderDiscount,
  ],
  serviceCharges: [
    {
      id: v4(),
      name: v4(),
      serviceChargeAmount: {
        currency: ISO4217.AUD,
        value: '50',
      },
    } as OrderServiceCharge,
  ],
  payments: [
    {
      id: v4(),
      entityUuid: txn.entityUuid,
      type: TransactionType.PURCHASE,
      status: OrderPaymentStatus.APPROVED,
      tenderType: TenderType.CARD,
      timestamp: '2024-12-11T02:33:05.731Z',
      transactionUuid: txn.id,
      amount: {
        currency: ISO4217.AUD,
        value: '10000',
      },
      amountTendered: {
        currency: ISO4217.AUD,
        value: '1000',
      },
      change: {
        currency: ISO4217.AUD,
        value: '2000',
      },
      taxAmounts: [
        {
          name: 'GST',
          amount: 100,
        },
      ],
      surchargeAmount: {
        currency: ISO4217.AUD,
        value: '10000',
      },
      tips: {
        currency: ISO4217.AUD,
        value: '10000',
      },
    } as OrderPayment,
  ],
} as Order;

const expectedPosReceipt = {
  abn: entity.abn,
  createdTimestamp: '2024-07-16T07:49:38.000Z',
  createdTimestampLocal: '2024-07-16T07:49:38.000+05:30',
  paidTimestampUTC: '2024-07-16T07:49:38.000Z',
  currency: 'AUD',
  businessName: 'Customer Name',
  dueAmount: 0,
  orderGst: 0,
  orderAmount: 0,
  orderReferenceNumber: 'ZPsxx',
  email: '<EMAIL>',
  facebook: 'facebook://myzeller',
  items: [
    {
      discounts: [
        {
          discountedAmount: '50',
          id: order.items![0].discounts![0].id,
          name: order.items![0].discounts![0].name,
        },
      ],
      serviceCharges: [
        {
          serviceChargeAmount: '50',
          id: order.items![0].serviceCharges![0].id,
          name: order.items![0].serviceCharges![0].name,
        },
      ],

      id: order.items![0].id,
      modifiers: [
        {
          id: order.items![0].modifiers![0].id,
          name: order.items![0].modifiers![0].name,
          price: '5035',
          subtotalAmount: '0',
        },
      ],
      name: order.items![0].name,
      price: '5035',
    },
  ],
  orderDiscounts: [
    {
      discountedAmount: '50',
      id: order.discounts![0].id,
      name: order.discounts![0].name,
    },
  ],
  orderServiceCharges: [
    {
      serviceChargeAmount: '50',
      id: order.serviceCharges![0].id,
      name: order.serviceCharges![0].name,
    },
  ],
  orderPayments: [
    {
      amount: 100,
      gst: 100,
      timestamp: '2020-12-16T17:17:59+00:00',
      timestampLocal: '2020-12-16T14:17:59+03:00',
      id: order.payments![0].id,
      orderPaymentShortId: '#1444',
      status: 'APPROVED',
      surchargeAmount: 100,
      tips: 100,
      amountTendered: 10,
      change: 20,
      taxAmounts: [
        {
          name: 'GST',
          amount: 100,
        },
      ],
      type: 'PURCHASE',
      tenderType: 'CARD',
      transactionReceiptUrl: `http://myzeller.dev/service/${txn.id}`,
      cardDetails: {
        cardholderUuid: txn.cardholderUuid,
        emvAid: '',
        emvAppName: '',
        emvTagsPrint: '',
        id: txn.id,
        maskedPan: '1111',
      },
    },
  ],
  orderUuid: order.id,
  paidAmount: 0,
  phone: '+***********',
  subtotalAmount: 80000,
  cashRoundingAdjustment: 0,
  taxInvoice: true,
  totalAmount: 80000,
  totalDiscount: 0,
  totalServiceCharge: 0,
  totalGst: 100,
  totalSurcharge: 0,
  totalTipAmount: 0,
  twitter: '@myzeller',
};

describe('receiptModelV2', () => {
  let receiptModel: ReceiptModelV2;
  const envService = {
    posReceiptBaseUrl: 'http://myzeller.dev/service/',
    smsBaseUrl: 'http://myzeller.dev/service/',
  } as any;
  beforeAll(() => {
    receiptModel = new ReceiptModelV2();
  });

  describe('convertMoneyType', () => {
    it('should return money type with correct values', () => {
      expect(receiptModel.convertMoneyType({ currency: ISO4217.AUD, value: '100' })).toEqual('100');
    });
  });

  describe('getTotalDiscount', () => {
    it('should return total discount with correct values', () => {
      expect(
        receiptModel.getTotalDiscount({
          totalDiscount: {
            value: '10000',
          } as any,
        } as any),
      ).toEqual(100);
    });
  });

  describe('getTotalServiceCharge', () => {
    it('should return total serviceCharge with correct values', () => {
      expect(
        receiptModel.getTotalServiceCharge({
          totalServiceCharge: {
            value: '10000',
          } as any,
        } as any),
      ).toEqual(100);
    });
  });

  describe('convertCentiCentsToCents', () => {
    it('should return cents from centi cents', () => {
      expect(receiptModel.convertCentiCentsToCents(100)).toEqual(1);
    });
  });

  describe('getSubtotalAmount', () => {
    it('should return subtotal amount with correct values', () => {
      expect(receiptModel.getSubtotalAmount(txn as any, { subtotalAmount: { value: '10000' } } as any)).toEqual(100);
      expect(receiptModel.getSubtotalAmount(txn as any)).toEqual(txn.saleAmount);
    });
  });

  describe('getGstValue', () => {
    it('should return gst value from getGstValue', () => {
      const transaction = {
        taxAmounts: [
          {
            name: 'GST',
            amount: 10,
          },
        ],
      } as any;
      expect(receiptModel.getGstValue(transaction)).toEqual(10);
    });
    it('should return 0 when gst not exist', () => {
      const transaction = {
        taxAmounts: [],
      } as any;
      expect(receiptModel.getGstValue(transaction)).toEqual(0);
    });
  });

  describe('formatMaskedPan', () => {
    it('should return masked pan with 4 digits and dots', () => {
      expect(receiptModel.formatMaskedPan('…1234')).toEqual('1234');
    });
    it('should return masked pan with 4 digits', () => {
      expect(receiptModel.formatMaskedPan('1234')).toEqual('1234');
    });
  });

  describe('getReceipt', () => {
    it('should return receipt with correct values', () => {
      expect(receiptModel.getReceipt(txn as any, site as any, entity as any, envService)).toEqual(
        JSON.parse(JSON.stringify(expectedReceipt)),
      );
    });

    it('should return receipt with correct values when receipt is empty', () => {
      const res = receiptModel.getReceipt({} as any, { receipt: {} } as any, entity as any, envService);
      expect(res.taxInvoice).toEqual(false);
      expect(res.type).toEqual('SALE');
    });

    it('should return receipt with correct reference when receipt type is REFUND', () => {
      expect(
        receiptModel.getReceipt({ ...txn, type: 'REFUND' } as any, site as any, entity as any, envService),
      ).toEqual(
        JSON.parse(
          JSON.stringify({
            ...expectedReceipt,
            type: 'REFUND',
            reference: expectedReceipt.rrn,
          }),
        ),
      );
    });

    it('should return receipt taxInvoice true', () => {
      const txnSaleAmountUnder1000AndWithGst = {
        ...txn,
        saleAmount: 80000,
        taxAmounts: [
          {
            name: 'GST',
            amount: 1000,
          },
        ],
      };

      expect(
        receiptModel.getReceipt(txnSaleAmountUnder1000AndWithGst as any, site as any, entity as any, envService, order),
      ).toEqual(
        JSON.parse(
          JSON.stringify({
            ...expectedReceipt,
            taxInvoice: false,
            orderReferenceNumber: 'ZPsxx',
            orderReceiptUrl: `http://myzeller.dev/service/${order.id}`,
            acn: undefined,
            saleAmount: 80000,
            subtotalAmount: 80000,
            totalGst: 1000,
            items: receiptModel.formatReceiptOrderItems(order.items),
            orderDiscounts: receiptModel.formatReceiptOrderDiscounts(order.discounts),
            orderServiceCharges: receiptModel.formatReceiptOrderServiceCharges(order.serviceCharges),
          }),
        ),
      );
    });

    it('should return receipt with items, discount and serviceCharges', () => {
      expect(receiptModel.getReceipt(txn as any, site as any, entity as any, envService, order)).toEqual(
        JSON.parse(
          JSON.stringify({
            ...expectedReceipt,
            acn: undefined,
            orderReferenceNumber: 'ZPsxx',
            orderReceiptUrl: `http://myzeller.dev/service/${order.id}`,
            subtotalAmount: 80000,
            items: receiptModel.formatReceiptOrderItems(order.items),
            orderDiscounts: receiptModel.formatReceiptOrderDiscounts(order.discounts),
            orderServiceCharges: receiptModel.formatReceiptOrderServiceCharges(order.serviceCharges),
          }),
        ),
      );
    });
  });

  describe('getPosReceipt', () => {
    it('should return pos receipt with correct values', () => {
      const transaction = JSON.parse(JSON.stringify(txn));
      transaction.taxAmounts = [
        {
          name: 'GST',
          amount: 100,
        },
      ];
      expect(
        receiptModel.getPosReceipt(
          order,
          site as any,
          entity as any,
          [transaction] as any,
          catalogItem as any,
          envService as any,
        ),
      ).toEqual(expectedPosReceipt);
    });

    it('should return pos receipt with catalog item data', () => {
      const transaction = JSON.parse(JSON.stringify(txn));
      transaction.taxAmounts = [
        {
          name: 'GST',
          amount: 100,
        },
      ];

      const expectedPosReceiptWithCatalogItem = {
        ...expectedPosReceipt,
        items: [
          {
            ...expectedPosReceipt.items[0],
            catalogItem: {
              id: catalogItem[0].id,
            },
            reportingCategoryColor: catalogItem[0].reportingCategoryColor,
            images: catalogItem[0].images!.map((image) => ({
              id: image.id,
              sizes: image.sizes,
            })),
          },
        ],
      };

      expect(
        receiptModel.getPosReceipt(
          { ...order, items: [{ ...order.items![0], catalogItem: { id: catalogItem[0].id } as CatalogItem }] },
          site as any,
          entity as any,
          [transaction] as any,
          catalogItem as any,
          envService as any,
        ),
      ).toEqual(expectedPosReceiptWithCatalogItem);
    });
  });

  it.each([
    [{ type: 'REFUND' }, 'REFUND'],
    [{ type: 'OTHER' }, 'SALE'],
    [{ source: 'ZELLER_INVOICE' }, 'INVOICE'],
    [{ source: 'VIRTUAL_TERMINAL' }, 'PAYMENT'],
  ])('should get correct ReceiptType for txn %s', (transaction, expected) => {
    expect(receiptModel.getReceiptType(transaction as any)).toEqual(expected);
  });
  it.each([
    [{ type: 'REFUND', externalReference: 'rrn', reference: 'reference' }, 'reference'],
    [{ type: 'OTHER', externalReference: 'rrn', reference: 'reference' }, 'reference'],
    [{ source: 'ZELLER_INVOICE', externalReference: 'externalReference' }, 'externalReference'],
    [{ source: 'VIRTUAL_TERMINAL', externalReference: 'externalReference' }, 'externalReference'],
  ])('should get correct getReference for txn %s', (transaction, expected) => {
    expect(receiptModel.getReference(transaction as any, receiptModel.getReceiptType(transaction as any))).toEqual(
      expected,
    );
  });

  it.each([
    { transaction: { isoProcessingCode: undefined }, expected: undefined },
    { transaction: { isoProcessingCode: '' }, expected: undefined },
    { transaction: { isoProcessingCode: '000000' }, expected: undefined },
    { transaction: { isoProcessingCode: '001000' }, expected: CardApplicationAccount.SAVINGS },
    { transaction: { isoProcessingCode: '002000' }, expected: CardApplicationAccount.CHEQUE },
    { transaction: { isoProcessingCode: '003000' }, expected: CardApplicationAccount.CREDIT },
  ])('should get correct account for txn %s', ({ transaction, expected }) =>
    expect(receiptModel.getCardApplicationAccount(transaction as Transaction)).toEqual(expected),
  );
});
