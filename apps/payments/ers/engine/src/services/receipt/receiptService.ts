import { NotFoundError } from '@npco/component-bff-core/dist/error';
import { debug, info } from '@npco/component-bff-core/dist/utils/logger';
import {
  EnvironmentService as DBSEnvService,
  DynamodbService,
  convertDbItemToTransaction,
} from '@npco/component-dbs-mp-common/dist';
import { convertDbItemToEntity } from '@npco/component-dbs-mp-common/dist/entity/convertDbItemToEntity';
import { getEntityDbItem } from '@npco/component-dbs-mp-common/dist/entity/getEntityDbItem';
import { convertDbItemToSite } from '@npco/component-dbs-mp-common/dist/site/convertDbItemToSite';
import { getSiteDbItem } from '@npco/component-dbs-mp-common/dist/site/getSiteDbItem';
import { getTransactionDbItemOrThrow } from '@npco/component-dbs-mp-common/dist/transaction/getTransactionDbItem';
import type { CatalogItem } from '@npco/component-dto-catalog/dist';
import { Source } from '@npco/component-dto-core/dist/';
import type { Order } from '@npco/component-dto-order/dist';
import { TenderType } from '@npco/component-dto-order/dist';
import { type Transaction, TransactionStatus } from '@npco/component-dto-transaction/dist';
// eslint-disable-next-line no-restricted-imports
import type { GetReceiptResponse, ReceiptData } from '@npco/receipt';
// eslint-disable-next-line no-restricted-imports
import {
  renderPosReceiptHtml,
  renderReceiptHtml,
  transformGetPosReceiptResponse,
  transformGetReceiptResponse,
} from '@npco/receipt';

import { ConfigService } from '@nestjs/config';

import { EnvironmentService } from '../../config';
import { CatalogRepository } from '../../repositories/catalogRepository';
import { OrderRepository } from '../../repositories/orderRepository';
import { PUBLIC_S3_PDF_RECEIPT_FOLDER, PUBLIC_S3_PNG_RECEIPT_FOLDER, ReceiptStorage } from '../storage/receiptStorage';

import { generatePdfFromHtml, generatePngFromHtml } from './htmlConverter/generateFileFromHtml';
import { ReceiptModelV2 } from './model/receiptModelV2';
import type { Receipt, SiteWithReceiptSettings } from './types';
import { FileFormat } from './types';

export class ReceiptService {
  constructor(
    private readonly dynamodbService: DynamodbService,
    private readonly dbsEnvService: DBSEnvService,
    private readonly envService: EnvironmentService,
    private readonly storage: ReceiptStorage,
    private readonly receiptModelV2: ReceiptModelV2,
    private readonly orderRepository: OrderRepository,
    private readonly catalogRepository: CatalogRepository,
  ) {}

  getReceipt = async (input: { transactionUuid: string }) => {
    // get transaction db record
    const rawTransaction = await getTransactionDbItemOrThrow(this.dynamodbService, '', input.transactionUuid, {
      filterDeleted: true,
      ignoreEntity: true,
    });
    // convert to transaction interface
    const transaction = convertDbItemToTransaction(rawTransaction);
    if (![TransactionStatus.APPROVED, TransactionStatus.DECLINED].includes(transaction.status as any)) {
      // receipt generation not allowed for other transaction statuses
      info('Receipt generation not allowed for transaction status: ', transaction.status);
      throw new NotFoundError('Transaction not found');
    }

    const getPromises: [
      Promise<Record<string, any> | null>,
      Promise<Record<string, any> | null>,
      Promise<Order | undefined>?,
    ] = [
      getSiteDbItem(this.dynamodbService, transaction.entityUuid, transaction.siteUuid),
      getEntityDbItem(this.dynamodbService, transaction.entityUuid),
    ];

    if (transaction.source === Source.ZELLER_POS && transaction.externalReference) {
      getPromises.push(this.orderRepository.getOrder(transaction.externalReference, transaction.entityUuid));
    }

    /**
     * Get additonal objects required to construct the receipt.
     */
    const [siteDbItem, entityDbItem, order] = await Promise.all(getPromises);
    if (!siteDbItem) {
      throw new NotFoundError(`Site ${transaction.siteUuid} not found`);
    }
    if (!entityDbItem) {
      throw new NotFoundError(`Entity ${transaction.entityUuid} not found`);
    }

    const site = convertDbItemToSite(siteDbItem) as SiteWithReceiptSettings; // NOSONAR;
    const entity = convertDbItemToEntity(entityDbItem);

    /**
     * Create receipt
     */
    return this.receiptModelV2.getReceipt(transaction, site, entity, this.envService, order);
  };

  getPosReceipt = async (input: { orderUuid: string }) => {
    const order = await this.orderRepository.getOrderByIdOrThrowNotFound(input.orderUuid);
    // get transaction db record
    const transactionPromises = (order.payments ?? [])
      .map(async (p) => {
        if (!p.tenderType || p.tenderType === TenderType.CARD) {
          const rawTransaction = await getTransactionDbItemOrThrow(this.dynamodbService, '', p.transactionUuid, {
            filterDeleted: true,
            ignoreEntity: true,
          });
          // convert to transaction interface
          const transaction = convertDbItemToTransaction(rawTransaction);
          if (![TransactionStatus.APPROVED, TransactionStatus.DECLINED].includes(transaction.status as any)) {
            // receipt generation not allowed for other transaction statuses
            info('Receipt generation not allowed for transaction status: ', transaction.status);
            throw new NotFoundError('Transaction not found');
          }
          return transaction;
        }
        return undefined;
      })
      .filter((tp) => tp !== undefined);

    const catalogItemIds = (order.items ?? []).map((item) => item.catalogItem?.id);
    const catalogItemPromises = catalogItemIds.map(async (id) =>
      this.catalogRepository.getCatalogItem(id as string, order.entityUuid),
    );

    const getPromises: [
      Promise<Record<string, any> | null>,
      Promise<Record<string, any> | null>,
      Promise<Transaction[]>,
      Promise<CatalogItem[]>?,
    ] = [
      getSiteDbItem(this.dynamodbService, order.entityUuid, order.siteUuid),
      getEntityDbItem(this.dynamodbService, order.entityUuid),
      Promise.all(transactionPromises) as Promise<Transaction[]>,
      Promise.all(catalogItemPromises) as Promise<CatalogItem[]>,
    ];

    /**
     * Get additonal objects required to construct the receipt.
     */
    const [siteDbItem, entityDbItem, transactions, catalogItems] = await Promise.all(getPromises);

    if (!siteDbItem) {
      throw new NotFoundError(`Site ${order.siteUuid} not found`);
    }
    if (!entityDbItem) {
      throw new NotFoundError(`Entity ${order.entityUuid} not found`);
    }

    const site = convertDbItemToSite(siteDbItem) as SiteWithReceiptSettings; // NOSONAR;
    const entity = convertDbItemToEntity(entityDbItem);

    /**
     * Create receipt
     */
    return this.receiptModelV2.getPosReceipt(
      order,
      site,
      entity,
      transactions.filter((t) => !!t),
      catalogItems,
      this.envService,
    );
  };

  numberToString = (num: number | undefined | null): string | null => {
    if (num) {
      return String(num);
    }
    return null;
  };

  convertReceiptToGetReceiptResponse = (receipt: Receipt): GetReceiptResponse => {
    return {
      ...receipt,
      status: receipt.status as any,
      items: receipt.items as any,
      orderDiscounts: receipt.orderDiscounts as any,
      orderServiceCharges: receipt.orderServiceCharges,
      subtotalAmount: this.numberToString(receipt.subtotalAmount),
      totalAmount: this.numberToString(receipt.totalAmount) as string,
      totalDiscount: this.numberToString(receipt.totalDiscount),
      totalServiceCharge: this.numberToString(receipt.totalServiceCharge),
      totalGst: this.numberToString(receipt.totalGst),
      totalSurcharge: this.numberToString(receipt.totalSurcharge),
      totalTips: this.numberToString(receipt.totalTips),
      saleAmount: this.numberToString(receipt.saleAmount),
    } as GetReceiptResponse;
  };

  convertReceiptToHtmlInput = (receipt: Receipt): ReceiptData => {
    return transformGetReceiptResponse(this.convertReceiptToGetReceiptResponse(receipt));
  };

  getReceiptDownloadUrl = async (input: { transactionUuid: string; format: FileFormat }) => {
    const { transactionUuid, format } = input;
    const receipt = await this.getReceipt({ transactionUuid });
    const receiptHtmlInput = this.convertReceiptToHtmlInput(receipt);
    const html = renderReceiptHtml({ receipt: receiptHtmlInput });
    let buffer;
    if (format === FileFormat.PDF) {
      buffer = await generatePdfFromHtml(html);
    } else {
      buffer = await generatePngFromHtml(html);
    }

    const fileName = `receipt-${transactionUuid}.${format.toLowerCase()}`;
    const folder = format === FileFormat.PDF ? PUBLIC_S3_PDF_RECEIPT_FOLDER : PUBLIC_S3_PNG_RECEIPT_FOLDER;
    const key = `${folder}/${fileName}`;
    debug('start to upload to s3');
    const contentType = format === FileFormat.PDF ? 'application/pdf' : 'image/png';
    const expire = await this.storage.uploadFileToS3(key, buffer, contentType);
    const receiptUrl = `${this.envService.pdfReceiptBaseUrl}${key}`;
    debug(`getReceiptDownloadUrl: ${receiptUrl} ${expire}`, transactionUuid);
    return { downloadLink: receiptUrl, expire };
  };

  getPosReceiptDownloadUrl = async (input: { orderUuid: string; format: FileFormat }) => {
    const { orderUuid, format } = input;
    const posReceipt = await this.getPosReceipt({ orderUuid });
    const posReceiptHtmlInput = transformGetPosReceiptResponse(posReceipt);
    const html = renderPosReceiptHtml({ receipt: posReceiptHtmlInput });
    let buffer;
    if (format === FileFormat.PDF) {
      buffer = await generatePdfFromHtml(html);
    } else {
      buffer = await generatePngFromHtml(html);
    }

    const fileName = `pos-receipt-${orderUuid}.${format.toLowerCase()}`;
    const folder = format === FileFormat.PDF ? PUBLIC_S3_PDF_RECEIPT_FOLDER : PUBLIC_S3_PNG_RECEIPT_FOLDER;
    const key = `${folder}/${fileName}`;
    debug('start to upload to s3');
    const contentType = format === FileFormat.PDF ? 'application/pdf' : 'image/png';
    const expire = await this.storage.uploadFileToS3(key, buffer, contentType);
    const receiptUrl = `${this.envService.pdfReceiptBaseUrl}${key}`;
    debug(`getReceiptDownloadUrl: ${receiptUrl} ${expire}`, orderUuid);
    return { downloadLink: receiptUrl, expire };
  };
}

export const createReceiptService = () => {
  const envDBS = new DBSEnvService();
  const configService = new ConfigService();
  const envErs = new EnvironmentService(configService);
  const dynamoDbService = new DynamodbService(envDBS);
  return new ReceiptService(
    dynamoDbService,
    envDBS,
    envErs,
    new ReceiptStorage(envErs),
    new ReceiptModelV2(),
    new OrderRepository(dynamoDbService, envErs),
    new CatalogRepository(dynamoDbService, envErs),
  );
};
