import type { CatalogImage } from '@npco/component-dto-catalog/dist/types';
import type { Address } from '@npco/component-dto-core/dist';
import type { OrderDiscount, OrderItem, OrderItemModifier, OrderServiceCharge } from '@npco/component-dto-order/dist/';
import type { ReceiptSettings, Site } from '@npco/component-dto-site/dist/';
import type { CardMedia, CardScheme, TransactionStatus } from '@npco/component-dto-transaction/dist';

export enum CardApplicationAccount {
  SAVINGS = 'SAVINGS',
  CHEQUE = 'CHEQUE',
  CREDIT = 'CREDIT',
}

export type Receipt = {
  transactionUuid: string;
  timestamp: string;
  timestampLocal: string;
  type: ReceiptType;
  reference?: string;
  rrn?: string;
  currency: string;
  totalDiscount?: number;
  totalServiceCharge?: number;
  totalAmount: number;
  saleAmount?: number;
  totalGst: number;
  totalSurcharge?: number;
  totalTips?: number;
  subtotalAmount: number;
  status: TransactionStatus;
  scheme: CardScheme;
  maskedPan: string;
  emvAid?: string;
  emvTagsPrint?: string;
  emvAppName?: string;
  cardMedia?: CardMedia;
  account?: CardApplicationAccount;
  cardholderUuid: string;
  taxInvoice: boolean;
  businessName: string;
  siteName?: string;
  catid?: string;
  address?: Address;
  abn?: string;
  acn?: string;
  email?: string;
  website?: string;
  phone?: string;
  logo?: string;
  returnMessage?: string;
  message?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  linkedin?: string;
  items?: ReceiptOrderItem[];
  orderDiscounts?: ReceiptOrderDiscount[];
  orderServiceCharges?: ReceiptOrderServiceCharge[];
};

export enum ReceiptType {
  SALE = 'SALE',
  REFUND = 'REFUND',
  INVOICE = 'INVOICE',
  PAYMENT = 'PAYMENT',
}

export enum FileFormat {
  PDF = 'PDF',
  PNG = 'PNG',
}

export type ReceiptOrderItem = Omit<
  OrderItem,
  'price' | 'subtotalAmount' | 'totalAmount' | 'modifiers' | 'discounts' | 'serviceCharges'
> & {
  price: string;
  subtotalAmount?: string;
  totalAmount?: string;
  discounts?: ReceiptOrderDiscount[];
  serviceCharges?: ReceiptOrderServiceCharge[];
  modifiers?: ReceiptOrderItemModifier[];
  reportingCategoryColor?: string;
  images?: Pick<CatalogImage, 'id' | 'sizes'>[];
};

export type ReceiptOrderDiscount = Omit<OrderDiscount, 'discountedAmount'> & {
  discountedAmount: string;
};

export type ReceiptOrderServiceCharge = Omit<OrderServiceCharge, 'serviceChargeAmount'> & {
  serviceChargeAmount: string;
};

export type ReceiptOrderItemModifier = Omit<OrderItemModifier, 'price' | 'subtotalAmount'> & {
  price: string;
  subtotalAmount: string;
};

export type SiteWithReceiptSettings = Site & { receipt: ReceiptSettings };
